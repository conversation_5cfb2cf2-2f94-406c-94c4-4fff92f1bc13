'use client';

import { useRouter } from 'next/navigation';
;import React from 'react';

type CreateEventLayoutProps = {
  title?: string;
  subTitle?: string;
  children: React.ReactNode;
  footer?: React.ReactNode;
  onBackPress?: () => void;
};

export const CreateEventLayout: React.FC<CreateEventLayoutProps> = ({
  title,
  subTitle,
  children,
  footer,
  onBackPress,
}) => {
  const router = useRouter();

  return (
    <div className="flex flex-col min-h-screen px-safe pt-safe pb-safe">
      <header className="px-4 py-3">
      </header>

      <main className="flex flex-col mx-4 mt-4 gap-1">
        {title && <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">{title}</h1>}
        {subTitle && (
          <p className="text-gray-600 dark:text-gray-400">{subTitle}</p>
        )}
      </main>

      <section className="flex flex-col gap-4 p-4">{children}</section>

      {footer && (
  <footer className="mt-auto px-4 pb-4 flex justify-end">
    <div className="w-full sm:w-auto">{footer}</div>
  </footer>
)}
    </div>
  );
};