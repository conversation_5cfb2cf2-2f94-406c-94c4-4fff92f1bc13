'use client';

import React from 'react';

type CreateEventLayoutProps = {
  title?: string;
  subTitle?: string;
  children: React.ReactNode;
  footer?: React.ReactNode;
};

export const CreateEventLayout: React.FC<CreateEventLayoutProps> = ({
  title,
  subTitle,
  children,
  footer,
}) => {
  return (
    <div className="flex flex-col min-h-screen w-full md:w-[500px]">
      <main className="mx-4 mt-4 gap-1">
        <div className="text-sm text-muted-foreground mb-6">
          <span className="font-medium text-white">Events</span>
          <span className="mx-1">/</span>
          <span className="text-muted-foreground">Create</span>
        </div>
        {title && (
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {title}
          </h1>
        )}
        {subTitle && (
          <p className="text-gray-600 dark:text-gray-400">{subTitle}</p>
        )}
      </main>

      <section className="flex flex-col flex-grow gap-4 p-4">
        {children}
      </section>

      {footer && (
        <footer className="px-4 pb-4 flex justify-end">
          <div className="w-full sm:w-auto">{footer}</div>
        </footer>
      )}
    </div>
  );
};
