import React from 'react';
import { Md<PERSON><PERSON><PERSON><PERSON><PERSON>, Small } from '@/components/ui';
import { AnimatedBorder } from '@/components/ui';
import { cn } from '@/lib/utils';
import Image from 'next/image';

interface GradientBorderCardProps {
  title: string;
  description?: string;
  icon: string;
  isSelected: boolean;
  onClick?: () => void;
}

export function GradientBorderCard({
  title,
  description,
  onClick,
  icon,
  isSelected,
}: GradientBorderCardProps) {
  const [borderWidth, setBorderWidth] = React.useState<number | null>(null);

  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      setBorderWidth(window.innerWidth - 32);
    }
  }, []);

  return (
    <div className="relative">
      <button
        onClick={onClick}
        className={cn(
          'relative w-full px-4 py-3 rounded-lg h-[92px] flex items-center justify-center bg-transparent',
          isSelected
            ? 'border border-border-subtle-light dark:border-border-subtle-dark'
            : ''
        )}
      >
        {/* {isSelected && borderWidth && (
          <AnimatedBorder
            width={borderWidth}
            height={92}
            isSelected={isSelected}
            borderRadius={16}
            borderWidth={1.5}
            blurRadius={0}
          />
        )} */}

        <CardContent icon={icon} title={title} description={description} />
      </button>
    </div>
  );
}

interface CardContentProps {
  title: string;
  description?: string;
  icon: string;
}

const CardContent = ({ icon, title, description }: CardContentProps) => (
  <div className="relative z-10 flex w-full flex-row gap-4 items-center">
    <Image
      className="my-auto rounded object-cover"
      src={icon}
      alt={title}
      width={40}
      height={40}
    />
    {description ? (
      <div className="w-full max-w-[273px] flex flex-col gap-2">
        <MdBoldLabel>{title}</MdBoldLabel>
        <Small className="text-gray-600 dark:text-gray-400">{description}</Small>
      </div>
    ) : (
      <MdBoldLabel className="my-auto">{title}</MdBoldLabel>
    )}
  </div>
);