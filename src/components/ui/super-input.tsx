'use client';

import React, { useState } from 'react';
import type {
  Control,
  FieldValues,
  Path,
  RegisterOptions,
} from 'react-hook-form';
import { useController, useFormContext } from 'react-hook-form';
import { tv } from 'tailwind-variants';
import { FiEye, FiEyeOff } from 'react-icons/fi';

const inputTv = tv({
  slots: {
    container: 'relative',
    label:
      'font-aeonik-regular text-base/100 font-medium text-fg-muted-light dark:text-fg-muted-dark',
    input:
      'h-[52px] rounded-md border border-border-subtle-light bg-transparent p-3 font-aeonik-regular text-base/100 font-medium shadow-none dark:border-border-subtle-dark dark:text-white',
    iconContainer:
      'absolute left-3 top-3.5 text-accent-moderate dark:text-accent-moderate',
  },

  variants: {
    focused: {
      true: {
        input:
          'border border-accent-moderate pb-2 pt-5 dark:border-accent-moderate',
        iconContainer: '',
      },
    },
    filled: {
      true: {
        input: 'pb-2 pt-5',
        iconContainer: '',
      },
    },
    error: {
      true: {
        input: 'border-2 border-red-60 dark:border-red-80',
        label: 'dark:text-danger-80 text-red-80',
        iconContainer: '',
      },
    },
    disabled: {
      true: {
        input: 'inherit',
        iconContainer: '',
      },
    },
  },
  defaultVariants: {
    focused: false,
    filled: false,
    error: false,
    disabled: false,
  },
});

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement | HTMLTextAreaElement> {
  label?: string;
  disabled?: boolean;
  error?: string;
  isPassword?: boolean;
  icon?: any;
  iconClassName?: string;
  containerClassName?: string;
  inputClassName?: string;
  focusedInputClassName?: string;
  handleFieldBlur?: () => void;
  handleFieldUnBlur?: () => void;
  hideErrorMessage?: boolean;
  multiline?: boolean;
}

type TRule<T extends FieldValues> =
  | Omit<
      RegisterOptions<T>,
      'disabled' | 'valueAsNumber' | 'valueAsDate' | 'setValueAs'
    >
  | undefined;

export type RuleType<T extends FieldValues> = { [name in keyof T]: TRule<T> };
export type InputControllerType<T extends FieldValues> = {
  name: Path<T>;
  control: Control<T>;
  rules?: RegisterOptions<T, Path<T>>;
};

interface ControlledInputProps<T extends FieldValues>
  extends Omit<InputProps, 'name'>,
    InputControllerType<T> {}


export const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ label, error, isPassword, icon, hideErrorMessage,
    iconClassName = '',
    containerClassName = '',
    inputClassName = '',
    focusedInputClassName = '',
    value,
    onBlur,
    handleFieldBlur,
    handleFieldUnBlur,
     ...props }, ref) => {
    const [showPassword, setShowPassword] = useState(!isPassword);
    const styles = inputTv({ error: !!error, disabled: props.disabled });

    return (
      <div className={styles.container()}>
        {label && <label className={styles.label()}>{label}</label>}
        <div className='relative'>
          {icon && <div className={styles.iconContainer()}>{icon}</div>}
          <input
            {...props}
            ref={ref}
            type={
            isPassword
              ? (showPassword ? 'text' : 'password')
              : props.type ?? 'text'
          }
            className={styles.input()}
            placeholder=''
          />
          {isPassword && (
            <button
              type='button'
              onClick={() => setShowPassword(!showPassword)}
              className='absolute right-3 top-3.5 text-gray-500 dark:text-gray-400'
            >
              {showPassword ? <FiEye /> : <FiEyeOff />}
            </button>
          )}
        </div>
        {error && !hideErrorMessage && (
          <p className='text-red-500 dark:text-red-600 text-sm mt-1'>{error}</p>
        )}
      </div>
    );
  }
);

export const ControlledInput = <T extends FieldValues>(props: ControlledInputProps<T>) => {
  const { control: contextControl } = useFormContext<T>();
  const { name, control = contextControl, rules, onChange: propsOnChange, multiline, ...rest } = props;

  const { field, fieldState } = useController({ name, control, rules });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    field.onChange(e);         
    if (propsOnChange) propsOnChange(e as any);
  };

  if (multiline) {
  const styles = inputTv({ error: !!fieldState.error, disabled: rest.disabled });

  return (
    <div className={styles.container()}>
      {props.label && <label className={styles.label()}>{props.label}</label>}
      <textarea
        {...(rest as React.TextareaHTMLAttributes<HTMLTextAreaElement>)}
        ref={field.ref}
        value={field.value ?? ''}
        onChange={handleChange}
        onBlur={field.onBlur}
        aria-label={props.label}
        className={styles.input()}
        placeholder=''
      />
      {fieldState.error?.message && !props.hideErrorMessage && (
        <p className="text-red-500 dark:text-red-600 text-sm mt-1">
          {fieldState.error?.message}
        </p>
      )}
    </div>
  );
}

  return (
    <Input
      {...rest as React.InputHTMLAttributes<HTMLInputElement>}
      ref={field.ref}
      value={field.value ?? ''}
      onChange={handleChange}
      className="w-full rounded-md border p-2"
      onBlur={field.onBlur}
      error={props.hideErrorMessage ? undefined : fieldState.error?.message}
    />
  );
};
