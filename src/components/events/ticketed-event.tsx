'use client';
import Image from 'next/image';
import { useCallback, useState, useMemo } from 'react';
import { ISingleEvent } from '@/api';
import toast from 'react-hot-toast';
import { env } from '@/env.mjs';
import { Plus, Minus, CreditCard, Wallet } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib';
import { H3, MdRegularLabel, SmRegularLabel } from '@/components/ui/typography';
import { FloatingInput } from '@/components/ui/floating-input';
// @ts-ignore
import PaystackPop from '@paystack/inline-js';
import { z } from 'zod';

const paymentSchema = z.object({
  cardholderName: z.string().min(3, 'Enter a valid name'),
  cardNumber: z
    .string()
    .regex(/^\d{4}\s?\d{4}\s?\d{4}\s?\d{4}$/, 'Invalid card number'),
  expiry: z
    .string()
    .regex(/^(0[1-9]|1[0-2])\/?([0-9]{2})$/, 'Invalid expiry date'),
  cvc: z.string().regex(/^\d{3,4}$/, 'Invalid CVC'),
});

const formatCardNumber = (value: string) => {
  return value
    .replace(/\D/g, '')
    .slice(0, 16)
    .replace(/(.{4})/g, '$1 ')
    .trim();
};

const formatExpiry = (value: string) => {
  const digits = value.replace(/\D/g, '').slice(0, 4);
  if (digits.length === 0) return '';
  if (digits.length < 3) return digits;
  return digits.slice(0, 2) + '/' + digits.slice(2);
};

export function TicketedEventPage({ event }: { event: ISingleEvent }) {
  const router = useRouter();
  const { user } = useAuth();

  const [quantities, setQuantities] = useState<{ [key: string]: number }>({});

  const [paymentMethod, setPaymentMethod] = useState('card');

  const ticketData = {
    tickets: Object.entries(quantities).map(([id, qty]) => ({
      categoryId: id,
      quantity: qty,
    })),
  };

  const handleQuantityChange = (
    id: string,
    change: number,
    maxQuantity: number
  ) => {
    setQuantities((prev) => {
      const currentQty = prev[id] || 0;
      let newQty = currentQty + change;

      if (newQty < 0) newQty = 0;

      if (newQty > maxQuantity) newQty = maxQuantity;

      return { ...prev, [id]: newQty };
    });
  };

  const totalAmount = useMemo(() => {
    return Object.entries(event.ticketCategories).reduce(
      (sum, [_, ticket]) => sum + (quantities[ticket.id] || 0) * ticket.cost,
      0
    );
  }, [quantities, event.ticketCategories]);

  const [loading, setLoading] = useState<boolean>(false);

  const [cardholderName, setCardholderName] = useState('');
  const [cardNumber, setCardNumber] = useState('');
  const [expiry, setExpiry] = useState('');
  const [cvc, setCvc] = useState('');

  const handlePaystackPayment = useCallback(() => {
    if (!totalAmount) {
      toast.error('Invalid amount');
      return;
    }
    const validation = paymentSchema.safeParse({
      cardholderName,
      cardNumber,
      expiry,
      cvc,
    });

    if (!validation.success) {
      toast.error(validation.error.issues[0].message);
      return;
    }
    setLoading(true);
    const email = user?.email || '';
    const amountInKobo = totalAmount * 100;

    const paystack = new PaystackPop();

    paystack.newTransaction({
      key: env.NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY,
      email: email,
      amount: amountInKobo,
      currency: 'NGN',
      onSuccess: () => {
        setLoading(false);
        toast.success('Payment successful!');
        router.push('/my-tickets');
      },
      onCancel: () => {
        setLoading(false);
        toast('Payment cancelled');
      },
      metadata: {
        fullname: cardholderName,
        tickets: ticketData.tickets,
      },
    });
  }, [
    totalAmount,
    user,
    ticketData,
    router,
    cardholderName,
    cardNumber,
    expiry,
    cvc,
  ]);

  const handleWalletPayment = useCallback(() => {
    if (!totalAmount) {
      toast.error('Invalid amount');
      return;
    }
    toast.error('Wallet payment not available yet');
  }, [totalAmount]);

  return (
    <div className='grid grid-cols-1 md:grid-cols-2'>
      <div className='w-full flex flex-col gap-y-4'>
        <div className='relative'>
          <Image
            src={event.bannerUrl}
            alt='Event'
            width={400}
            height={500}
            className='rounded-lg object-cover'
          />
          {/* <div className='absolute top-4 right-4 bg-black text-center px-2 py-1 rounded-md'>
            <div className='text-xs font-semibold'>21</div>
            <div className='text-[10px] text-gray-300'>May</div>
          </div> */}
          <div className='absolute bottom-0 left-0 w-full bg-linear-to-t from-black/80 via-black/50 to-transparent p-4 rounded-b-lg'>
            <h2 className='text-lg font-semibold'>{event.title}</h2>
            <p className='text-sm text-gray-300'>{event.organizer.fullName}</p>
          </div>
        </div>

        <SmRegularLabel className='text-gray-400 text-sm mt-4'>
          {event.description}
        </SmRegularLabel>

        <div className='gap-y-[7px]'>
          <H3>Tickets</H3>

          {Object.entries(event.ticketCategories).map(([name, ticket]) => (
            <div
              key={ticket.id}
              className='flex justify-between items-center py-4'
            >
              <div className='flex flex-col gap-y-2'>
                <MdRegularLabel>{name}</MdRegularLabel>
                <SmRegularLabel className='text-gray-400 text-xs'>
                  {ticket.cost.toLocaleString()} NGN
                </SmRegularLabel>
              </div>

              <div className='flex items-center border border-gray-600 rounded-full overflow-hidden'>
                <button
                  onClick={() =>
                    handleQuantityChange(
                      ticket.id,
                      -1,
                      Math.min(ticket.quantity, 10)
                    )
                  }
                  className='w-6 h-6 flex items-center justify-center hover:bg-gray-700'
                >
                  <Minus className='w-3 h-3' />
                </button>
                <span className='px-3'>{quantities[ticket.id] || 0}</span>
                <button
                  onClick={() =>
                    handleQuantityChange(
                      ticket.id,
                      1,
                      Math.min(ticket.quantity, 10)
                    )
                  }
                  className='w-6 h-6 flex items-center justify-center hover:bg-gray-700'
                >
                  <Plus className='w-3 h-3' />
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className='w-full space-y-8 px-4'>
        <div className='space-y-2'>
          <H3>Payment details</H3>
          <SmRegularLabel className='text-fg-muted-light'>
            Complete your purchase below
          </SmRegularLabel>
        </div>

        <div className='space-y-1'>
          <label className='flex items-center justify-between rounded-lg cursor-pointer px-4 gap-x-4'>
            <div className='flex items-center gap-x-4 py-3'>
              <div className='bg-accent-subtle-light dark:bg-accent-subtle-dark p-2 rounded-full size-10 flex items-center justify-center'>
                <CreditCard className='w-4 h-4' />
              </div>
              <div className='flex flex-col space-y-2 py-px'>
                <MdRegularLabel>Card payment</MdRegularLabel>
                <SmRegularLabel className='text-fg-muted-light'>
                  Pay using debit card
                </SmRegularLabel>
              </div>
            </div>
            <input
              type='radio'
              name='payment'
              className='accent-purple-500'
              value='card'
              checked={paymentMethod === 'card'}
              onChange={() => setPaymentMethod('card')}
            />
          </label>
          <label className='flex items-center justify-between cursor-pointer px-4'>
            <div className='flex items-center gap-x-4 py-3'>
              <div className='bg-accent-subtle-light dark:bg-accent-subtle-dark p-2 rounded-full size-10 flex items-center justify-center'>
                <Wallet className='w-4 h-4' />
              </div>
              <div className='flex flex-col space-y-2 py-px'>
                <MdRegularLabel>Wallet</MdRegularLabel>
                <SmRegularLabel className='text-fg-muted-light'>
                  Available balance - 0.00 NGN
                </SmRegularLabel>
              </div>
            </div>
            <input
              type='radio'
              name='payment'
              className='accent-purple-500'
              value='wallet'
              checked={paymentMethod === 'wallet'}
              onChange={() => setPaymentMethod('wallet')}
            />
          </label>
        </div>
        {paymentMethod === 'card' && (
          <div className='space-y-4'>
            <FloatingInput
              placeholder='Cardholder name'
              label='Cardholder name'
              disabled={loading}
              type='text'
              value={cardholderName}
              onChange={(e) => setCardholderName(e.target.value)}
            />
            <FloatingInput
              label='Card number'
              disabled={loading}
              placeholder='Card number'
              type='text'
              value={cardNumber}
              onChange={(e) => setCardNumber(formatCardNumber(e.target.value))}
            />
            <div className='flex space-x-4'>
              <FloatingInput
                placeholder='Expiry date'
                disabled={loading}
                type='text'
                value={expiry}
                onChange={(e) => setExpiry(formatExpiry(e.target.value))}
                label='Expiry date'
              />
              <FloatingInput
                placeholder='CVC'
                disabled={loading}
                type='text'
                value={cvc}
                onChange={(e) => setCvc(e.target.value)}
                label='CVC'
              />
            </div>

            <FloatingInput
              placeholder='Discount code'
              disabled={loading}
              type='text'
              label='Discount code'
            />
          </div>
        )}

        <button
          className='w-full mt-6 bg-accent-moderate dark:bg-accent-moderate hover:bg-accent-moderate-dark transition-colors rounded-full py-3 font-semibold'
          disabled={loading || !totalAmount}
          onClick={
            paymentMethod === 'card'
              ? handlePaystackPayment
              : handleWalletPayment
          }
        >
          {totalAmount ? `Pay ${totalAmount.toLocaleString()} NGN` : 'Continue'}
        </button>
      </div>
    </div>
  );
}
